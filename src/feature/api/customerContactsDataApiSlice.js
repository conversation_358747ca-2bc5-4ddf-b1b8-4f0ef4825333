import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const customerContactsDataApiSlice = createApi({
  reducerPath: "customerContactsDataApiSlice",
  baseQuery,
  tagTypes: ["customerContactsData"],
  endpoints: (builder) => ({
    getCustomerContactsList: builder.query({
      query: (body) => ({
        url: `contacts/listContact`,
        method: "POST",
        body,
      }),
      providesTags: ["customerContactsData"],
    }),
    // listAllCustomerContacts: builder.query({
    //   query: (body) => ({
    //     url: `contacts/listAllCustomerContacts`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
    deleteCustomerContacts: builder.mutation({
      query: (body) => ({
        url: `contacts/deleteCustomerContact`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["customerContactsData"],
    }),
    createCustomerContacts: builder.mutation({
      query: (body) => ({
        url: `contacts/createContact`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["customerContactsData"],
    }),
    editCustomerContacts: builder.mutation({
      query: (body) => ({
        url: `contacts/editContact`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["customerContactsData"],
    }),
    // singleCustomer: builder.mutation({
    //   query: (body) => ({
    //     url: `customers/singleCustomer`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
  }),
});

export const {
  useGetCustomerContactsListQuery,
  // useListAllCustomerContactsQuery,
  useDeleteCustomerContactsMutation,
  useCreateCustomerContactsMutation,
  useEditCustomerContactsMutation,
//   useSingleCustomerContactsMutation,
} = customerContactsDataApiSlice;
