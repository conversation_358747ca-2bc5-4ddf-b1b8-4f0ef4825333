import { combineReducers } from "@reduxjs/toolkit";

import authState from "./slice/authSlice";
import appConfig from "./slice/appConfigSlice";
import { authApiSlice } from "./api/authApiSlice";
import { shopDataApiSlice } from "./api/shopDataApiSlice";
import { branchDataApiSlice } from "./api/branchDataApiSlice";
import { statusApiSlice } from "./api/statusApiSlice";
import { rolesDataApiSlice } from "./api/rolesDataApiSlice";
import { staffsDataApiSlice } from "./api/staffsDataApiSlice";
import { categoriesDataApiSlice } from "./api/categoriesDataApiSlice";
import { subCategoriesDataApiSlice } from "./api/subCategoriesDataApiSlice";
import { childCategoriesDataApiSlice } from "./api/childCategoriesDataApiSlice";
import { attributesDataApiSlice } from "./api/attributesDataApiSlice";
import { attributesValuesDataApiSlice } from "./api/attributesValuesDataApiSlice";
import { brandsDataApiSlice } from "./api/brandsDataApiSlice";
import { productDataApiSlice } from "./api/productDataApiSlice";
import { inventoryDataApiSlice } from "./api/inventoryDataApiSlice";
import { dashboardDataApiSlice } from "./api/dashboardDataApiSlice";
import { customerDataApiSlice } from "./api/customerDataApiSlice";
import { customerContactsDataApiSlice } from "./api/customerContactsDataApiSlice";

export const rootReducer = combineReducers({
  authState,
  appConfig,
  [authApiSlice.reducerPath]: authApiSlice.reducer,
  [shopDataApiSlice.reducerPath]: shopDataApiSlice.reducer,
  [branchDataApiSlice.reducerPath]: branchDataApiSlice.reducer,
  [statusApiSlice.reducerPath]: statusApiSlice.reducer,
  [rolesDataApiSlice.reducerPath]: rolesDataApiSlice.reducer,
  [staffsDataApiSlice.reducerPath]: staffsDataApiSlice.reducer,
  [categoriesDataApiSlice.reducerPath]: categoriesDataApiSlice.reducer,
  [subCategoriesDataApiSlice.reducerPath]: subCategoriesDataApiSlice.reducer,
  [childCategoriesDataApiSlice.reducerPath]: childCategoriesDataApiSlice.reducer,
  [attributesDataApiSlice.reducerPath]: attributesDataApiSlice.reducer,
  [attributesValuesDataApiSlice.reducerPath]: attributesValuesDataApiSlice.reducer,
  [brandsDataApiSlice.reducerPath]: brandsDataApiSlice.reducer,
  [productDataApiSlice.reducerPath]: productDataApiSlice.reducer,
  [inventoryDataApiSlice.reducerPath]: inventoryDataApiSlice.reducer,
  [dashboardDataApiSlice.reducerPath]: dashboardDataApiSlice.reducer,
  [customerDataApiSlice.reducerPath]: customerDataApiSlice.reducer,
  [customerContactsDataApiSlice.reducerPath]: customerContactsDataApiSlice.reducer,
});
