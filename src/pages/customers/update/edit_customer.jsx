import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useGetUserStatusQuery } from "../../../feature/api/statusApiSlice";
import FormikField from "../../../components/formikField";
import {
  useEditCustomerMutation,
} from "../../../feature/api/customerDataApiSlice";
import { useLocation, useNavigate } from "react-router-dom";
import WebLoader from "../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";

const validation = yup.object().shape({
  customer_name: yup.string().required().label("Customer Name"),
  phone_code: yup.string().required().label("Phone Code"),
  phone: yup.string().required().label("Phone"),
  email: yup.string().email().label("Email"),
  qid: yup.string().length(11).required().label("Qatar ID"),
  billing_address: yup.string().label("Billing Address"),
  shipping_address: yup.string().label("Shipping Address"),
  tax_id: yup.string().label("Tax ID"),
  payment_terms: yup.string().label("Payment Terms"),
  credit_limit: yup.string().label("Credit Limit"),
  status: yup.string().required().label("Status"),
  remarks: yup.string().label("Remarks"),
  branch_id: yup.string().required().label("Branch"),
});
export default function EditCustomer() {
  const navigate = useNavigate(); // Initialize the navigation hook
  const { state } = useLocation();
    const customerData = state;
  const activePage = "Customers Master";
  const linkHref = "/dashboard";

  const initialValues = {
    customer_name: customerData?.customer_name || null,
    phone_code: customerData?.phone_code || "",
    phone: customerData?.phone || "",
    email: customerData?.email || "",
    qid: customerData?.qid || "",
    billing_address: customerData?.billing_address || "",
    shipping_address: customerData?.shipping_address || "",
    tax_id: customerData?.tax_id || "",
    payment_terms: customerData?.payment_terms || "",
    credit_limit: customerData?.credit_limit || "",
    status: customerData?.status || "",
    remarks: customerData?.remarks || "",
    branch_id: customerData?.branch_id || "",
  };

  /* **************** Start list User Status ******************* */
  const userStatusData = useGetUserStatusQuery();
  const userStatusDataList = userStatusData?.data?.data || [];
  const userStatusList = userStatusDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list User Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + ' (' + values.branch_type_name + ')',
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Edit Customer ******************* */
  const [handleEditCustomerApi, {isLoading: isLoading}] = useEditCustomerMutation();
  const handleSubmit = async (body) => {
    try {
      const createBody = {
        ...body,
        customer_id: parseInt(customerData?.id),
        branch_id: parseInt(body.branch_id),
        status: parseInt(body.status)
      };
      const resp = await handleEditCustomerApi(createBody).unwrap();
      handleApiSuccess(resp);
      navigate("/customers");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Customer ******************* */

  /* **************** Web Loader  ******************* */
      if (isLoading)
        return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Customer Details</h4>
                            <p className="card-subtitle mb-4">
                              To update Customer, add details and save from here
                            </p>
                            <Formik
                              initialValues={initialValues}
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              <Form
                                name="product-create"
                                className="needs-validation"
                                autoComplete="off"
                                encType="multipart/form-data"                      
                              >
                                <div className="row">     
                                <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_id"
                                        className="form-label"
                                      >
                                        Branch 
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="branch_id"
                                        id="branch_id"
                                        className="form-select"
                                        type="select"
                                        options={branchesList}
                                      />
                                    </div>
                                  </div>                             
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="customer_name"
                                        className="form-label"
                                      >
                                        Customer Name
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="customer_name"
                                        id="customer_name"
                                        placeholder="Customer Name *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label className="form-label">
                                        Phone Number{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <FormikField
                                            name="phone_code"
                                            id="phone_code"
                                            className="form-select"
                                            type="select"
                                            options={[
                                              {
                                                value: "+974",
                                                label: "+974 - Qatar",
                                              },
                                            ]}
                                          />
                                        </div>
                                        <div className="col-8">
                                          <FormikField
                                            type="number"
                                            name="phone"
                                            id="phone"
                                            placeholder="Phone Number *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="email"
                                        className="form-label"                                
                                      >
                                        Email                                      
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="email"
                                        id="email"
                                        placeholder="Email"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_email"
                                        className="form-label"
                                      >
                                        Qatar ID{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="qid"
                                        id="qid"
                                        placeholder=" Qatar ID *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="billing_address"
                                        className="form-label"
                                      >
                                        Billing Address
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="billing_address"
                                        id="billing_address"
                                        placeholder="Billing Address"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shipping_address"
                                        className="form-label"
                                      >
                                        Shipping Address
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="shipping_address"
                                        id="shipping_address"
                                        placeholder="Shipping Address"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>  
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="tax_id"
                                        className="form-label"
                                      >
                                        Tax ID
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="tax_id"
                                        id="tax_id"
                                        placeholder="Tax ID"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="payment_terms"
                                        className="form-label"
                                      >
                                        Payment Terms
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="payment_terms"
                                        id="payment_terms"
                                        placeholder="Payment Terms"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="credit_limit"
                                        className="form-label"
                                      >
                                        Credit Limit
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="credit_limit"
                                        id="credit_limit"
                                        placeholder="Credit Limit"
                                        autoComplete="off"
                                        className="form-control"
                                        step="0.01"
                                      />
                                    </div>
                                  </div> 
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="remarks"
                                        className="form-label"
                                      >
                                        Remarks
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="remarks"
                                        id="remarks"
                                        placeholder="Remarks"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>                             
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="status"
                                        className="form-label"
                                      >
                                        Status
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="status"
                                        id="status"
                                        className="form-select"
                                        type="select"
                                        options={userStatusList}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Edit Customer
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
