import { useMemo, useState } from "react";
import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { Form } from "react-router-dom";
import { Table } from "../../components/datatable";
import { PaginationComponent } from "../../components/pagination";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import {
  useCreateRoleMutation,
  useDeleteRoleMutation,
  useEditRoleMutation,
  useGetRolesQuery,
} from "../../feature/api/rolesDataApiSlice";
import { Formik } from "formik";
import FormikField from "../../components/formikField";
import * as yup from "yup";
import WebLoader from "../../components/webLoader";

import Modal from "react-bootstrap/Modal";
import useConfirmDelete from "../../hooks/useConfirmDelete";

const initialValues = {
  role_name: "",
  role_name_ar: "",
};

const validation = yup.object().shape({
  role_name: yup.string().required().label("Role name"),
  role_name_ar: yup.string().required().label("Role name in Arabic"),
});
export default function RoleList() {
  /* **************** Role Add Model ******************* */
  const [showAddModel, setShowAddModel] = useState(false);
  const handleAddModelClose = () => setShowAddModel(false);
  const handleAddModelShow = () => setShowAddModel(true);
  /* **************** End Role Add Model ******************* */
  /* **************** Role Edit Model ******************* */
  const [showEditModel, setShowEditModel] = useState(false);
  const handleEditModelClose = () => setShowEditModel(false);
  const handleEditModelShow = () => setShowEditModel(true);
  /* **************** End Role Edit Model ******************* */
  const activePage = "Roles Master";
  const linkHref = "/dashboard";

  /* **************** Start list all Roles ******************* */

  const [currentPage, setCurrentPage] = useState(1);
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: rolesData, isLoading } = useGetRolesQuery({
    page: currentPage,
    keywords: filterKeywords,
  });
  const rolesList = useMemo(() => {
    if (!rolesData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return rolesData?.data?.list;
  }, [currentPage, rolesData?.data.list]);

  const pageData = useMemo(() => rolesData?.data?.page ?? null, [rolesData]);

  /* ****************  Start Filter ****************** */
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */

  /* **************** Fetch data when page changes ******************* */
  const fetchData = async (page) => {
    console.log(`Fetching data for page ${page}`);
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End list all Roles ******************* */
  /* **************** Start Delete role ******************* */

   const { showSimpleConfirm } = useConfirmDelete({
                  title: 'Delete Role?',
                  text: 'Are you sure you want to delete this role?',
                  confirmButtonText: 'Yes, delete role!',
                  cancelButtonText: 'Cancel'
              });

  const [handledeleteRoleApi, { isLoading: isDeleteLoading }] =
    useDeleteRoleMutation();
  const onDeleteRoleHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { role_id: id };
          const resp = await handledeleteRoleApi(body).unwrap();
          handleApiSuccess(resp);
        } catch (error) {
          handleApiErrors(error);
        }
    }
  };
  /* **************** End Delete role ******************* */
  /* **************** Create new Role ******************* */
  const [handleCreateRoleApi, { isLoading: isCreateLoading }] =
    useCreateRoleMutation();
  const handleSubmitFunction = async (body) => {
    try {
      const resp = await handleCreateRoleApi(body).unwrap();
      handleApiSuccess(resp);
      handleAddModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create new Role ******************* */
  /* **************** Edit Role ******************* */
  const [editValues, setEditValues] = useState({
    role_id: "",
    role_name: "",
    role_name_ar: "",
  });
  const handleOpenModal = (values) => {
    setEditValues(values);
    handleEditModelShow();
  };
  const onEditRoleDetailsHandler = (d) => {
    handleOpenModal({
      role_id: d?.id || "",
      role_name: d?.role_name || "",
      role_name_ar: d?.role_name_ar || "",
    });
  };
  const [handleEditRoleApi, { isLoading: isEditLoading }] =
    useEditRoleMutation();
  const RoleUpdateFormSubmit = async (body) => {
    try {
      const resp = await handleEditRoleApi(body).unwrap();
      handleApiSuccess(resp);
      handleEditModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Role ******************* */
  /* **************** Web Loader  ******************* */
  if (isLoading || isCreateLoading || isEditLoading || isDeleteLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Role List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <button
                          type="button"
                          className="btn btn-primary"
                          onClick={handleAddModelShow}
                          // data-bs-toggle="modal"
                          // data-bs-target="#createRole"
                        >
                          Create New Role
                        </button>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">                      
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "role_name",
                        label: "Role Name",
                        align: "left",
                      },
                      {
                        key: "role_name_ar",
                        label: "Role Name in Arabic",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                      {
                        key: "updated_at",
                        label: "Updated At",
                        align: "left",
                      },
                    ]}
                    data={rolesList}
                    onDeleteHandler={onDeleteRoleHandler}
                    onEditHandler={onEditRoleDetailsHandler}
                  />
                </div>
                <PaginationComponent
                  totalCount={pageData?.total_count}
                  pageSize={pageData?.page_size}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  onPageChange={fetchData}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Modal --> */}

      <Modal
        show={showAddModel}
        onHide={handleAddModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create New Role</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={initialValues}
            validationSchema={validation}
            onSubmit={handleSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
              >
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_name" className="form-label">
                          Role Name in English:
                        </label>
                        <FormikField
                          type="text"
                          name="role_name"
                          id="role_name"
                          placeholder="Role Name  in English *"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_name" className="form-label">
                          Role Name in Arabic:
                        </label>
                        <FormikField
                          type="text"
                          name="role_name_ar"
                          id="role_name_ar"
                          placeholder="Role Name  in Arabic *"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                    data-bs-dismiss="modal"
                    onClick={handleAddModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Create Role
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
        {/* <Modal.Footer></Modal.Footer> */}
      </Modal>
      {/* <!-- Modal end --> */}
      {/* <!-- EditModal --> */}

      <Modal
        show={showEditModel}
        onHide={handleEditModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title> Update Role Data</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={editValues}
            enableReinitialize
            validationSchema={validation}
            onSubmit={RoleUpdateFormSubmit}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-update"
                className="needs-validation"
                autoComplete="off"
              >
                <FormikField
                  type="hidden"
                  name="role_id"
                  id="role_id"
                  autoComplete="off"
                  className="form-control"
                />
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_name" className="form-label">
                          Role Name in English:
                        </label>
                        <FormikField
                          type="text"
                          name="role_name"
                          id="role_name"
                          placeholder="Role Name  in English *"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_name" className="form-label">
                          Role Name in Arabic:
                        </label>
                        <FormikField
                          type="text"
                          name="role_name_ar"
                          id="role_name_ar"
                          placeholder="Role Name  in Arabic *"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                    data-bs-dismiss="modal"
                    onClick={handleEditModelClose}
                  >
                    Close
                  </button>
                  <button className="btn btn-primary" onClick={handleSubmit}>
                    Update Role
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
        {/* <Modal.Footer></Modal.Footer> */}
      </Modal>
      {/* <!-- EditModal end --> */}
      <CommonFooter />
    </>
  );
}
