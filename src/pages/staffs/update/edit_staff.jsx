import { useLocation, useNavigate } from "react-router-dom";
import * as yup from "yup";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useGetRolesQuery } from "../../../feature/api/rolesDataApiSlice";
import { useGetStaffStatusQuery } from "../../../feature/api/statusApiSlice";
import CommonFooter from "../../../components/layout/common_footer";
import CommonHeader from "../../../components/layout/common_header";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import FormikField from "../../../components/formikField";
import { Form, Formik } from "formik";
import { handleCustomError } from "../../../hooks/handleCustomError";
import { useMemo } from "react";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { useUpdateStaffMutation } from "../../../feature/api/staffsDataApiSlice";
import WebLoader from "../../../components/webLoader";
const initialValues = {
  branch_id: "",
  staff_role_id: "",
  name: "",
  email: "",
  phone_code: "",
  phone: "",
  qid: "",
  address: "",
  status: "",
};
const validation = yup.object().shape({
  branch_id: yup.string().required().label("Branch"),
  staff_role_id: yup.string().required().label("Staff Role"),
  name: yup.string().required().label("Staff Name"),
  email: yup.string().email().required().label("Email Address"),
  phone_code: yup.string().required().label("Phone Number"),
  phone: yup
    .string()
    .matches(/^\d{8}$/, "Phone number must be exactly 8 digits")
    .required()
    .label("Phone Number"),
  qid: yup.string().length(11).required().label("Qatar ID"),
  address: yup.string().required().label("Address"),
  status: yup.string().required().label("Status"),
});
const EditStaff = () => {
  const { state } = useLocation();
  const staffData = state;
  const navigation = useNavigate();

  if (!staffData) {
    handleCustomError("Error was found, please try again later!");
    navigation("/staffs");
  }

  const EditStaffValues = useMemo(
    () =>
      !staffData
        ? initialValues
        : {
            branch_id: staffData?.branch_id || "",
            staff_role_id: staffData?.staff_role_id || "",
            name: staffData?.name || "",
            email: staffData?.email || "",
            phone_code: staffData?.phone_code || "",
            phone: staffData?.phone || "",
            qid: staffData?.qid || "",
            address: staffData?.address || "",
            status: staffData?.status || "",
          },
    [staffData]
  );

  const activePage = "Edit Staff";
  const linkHref = "/dashboard";
  /* **************** Start list all Branchs ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchListAry = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name,
  }));
  /* **************** End list all Branchs ******************* */

  /* **************** Start list all Staff Roles ******************* */
  let { data: rolesData } = useGetRolesQuery({
    page: 1,
  });
  const rolesDataList = rolesData?.data?.list || [];

  const rolesDataAry = rolesDataList.map((values) => ({
    value: values.id,
    label: values.role_name,
  }));
  /* **************** End list all Staff Roles ******************* */

  /* **************** Start list all Staff Status ******************* */
  const staffStatusData = useGetStaffStatusQuery();

  const staffStatusDataRes = staffStatusData.data?.data || [];
  const staffStatusList = staffStatusDataRes.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list all Staff Status ******************* */

  /* **************** Start Update Staff Data ******************* */
  const [handleUpdateStaffApi, { isLoading: isLoading }] = useUpdateStaffMutation();
  const formSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        staff_id: parseInt(staffData?.id),
        branch_id: parseInt(body.branch_id),
        staff_role_id: parseInt(body.staff_role_id),
        status: parseInt(body.status),
      };
      const resp = await handleUpdateStaffApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      navigation("/staffs"); // Redirect to the desired page
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Update Staff Data******************* */

  /* **************** Start Web Loader  ******************* */
            if (isLoading)
              return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Staff Details</h4>
                            <p className="card-subtitle mb-4">
                              To update Staff , edit details and save from here
                            </p>
                            <Formik
                              initialValues={EditStaffValues}
                              enableReinitialize
                              validationSchema={validation}
                              onSubmit={formSubmit}
                            >
                              <Form
                                name="branch-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_name"
                                        className="form-label"
                                      >
                                        Branch{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <FormikField
                                        name="branch_id"
                                        id="branch_id"
                                        className="form-select"
                                        type="select"
                                        options={branchListAry}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_name"
                                        className="form-label"
                                      >
                                        Staff Role{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <FormikField
                                        name="staff_role_id"
                                        id="staff_role_id"
                                        className="form-select"
                                        type="select"
                                        options={rolesDataAry}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_name"
                                        className="form-label"
                                      >
                                        Staff Name{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="name"
                                        id="name"
                                        placeholder=" Staff Name *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_email"
                                        className="form-label"
                                      >
                                        Staff Email Address{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <FormikField
                                        type="email"
                                        name="email"
                                        id="email"
                                        placeholder=" Staff Email Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label className="form-label">
                                        Phone Number{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <FormikField
                                            name="phone_code"
                                            id="phone_code"
                                            className="form-select"
                                            type="select"
                                            options={[
                                              {
                                                value: "+974",
                                                label: "+974 - Qatar",
                                              },
                                            ]}
                                          />
                                        </div>

                                        <div className="col-8">
                                          <FormikField
                                            type="number"
                                            name="phone"
                                            id="phone"
                                            placeholder="Phone Number *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_email"
                                        className="form-label"
                                      >
                                        Qatar ID{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="qid"
                                        id="qid"
                                        placeholder=" Qatar ID *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_email"
                                        className="form-label"
                                      >
                                        Address{" "}
                                        <sp className="un-validation">*</sp>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="address"
                                        id="address"
                                        placeholder=" Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_name"
                                        className="form-label"
                                      >
                                        Status
                                      </label>
                                      <FormikField
                                        name="status"
                                        id="status"
                                        className="form-select"
                                        type="select"
                                        options={staffStatusList}
                                      />
                                    </div>
                                  </div>

                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Update Staff
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
};

export default EditStaff;
